import { json } from '@sveltejs/kit';
import { cancelPayment } from '$lib/mp/index.mp.js';

export async function POST({ params }) {
  try {
    const { paymentId } = params;

    if (!paymentId) {
      return json(
        { error: 'ID do pagamento não fornecido' },
        { status: 400 }
      );
    }

    const result = await cancelPayment(paymentId);

    if (!result.success) {
      return json(
        { error: result.error, details: result.details },
        { status: 400 }
      );
    }

    return json(result);

  } catch (error) {
    console.error('Erro no endpoint /api/payment-cancel:', error);
    return json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 