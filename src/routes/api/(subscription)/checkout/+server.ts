import { json } from '@sveltejs/kit';
import { createPayment } from '$lib/mp/index.mp.js';
import type { CreatePaymentApiRequest } from '$lib/mp/types.mp.js';

export async function POST({ request }) {
  try {
    const body: CreatePaymentApiRequest = await request.json();
    const { planType, userData } = body;

    const result = await createPayment(planType, userData);

    if (!result.success) {
      return json(
        { error: result.error, details: result.details },
        { status: 400 }
      );
    }

    return json(result);

  } catch (error) {
    console.error('Erro no endpoint /api/checkout:', error);
    return json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
} 