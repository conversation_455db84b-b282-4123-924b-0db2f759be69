import { json } from '@sveltejs/kit';
import { processWebhook, verifyWebhookSignature } from '$lib/mp/webhook.mp.js';
import type { WebhookData } from '$lib/mp/types.mp.js';

export async function POST({ request }) {
  try {
    // Extract required headers for signature verification
    const signature = request.headers.get('x-signature');
    const requestId = request.headers.get('x-request-id');

    // Check if required headers are present
    if (!signature || !requestId) {
      console.error('❌ Headers de autenticação ausentes:', { signature: !!signature, requestId: !!requestId });
      return json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get raw body for signature verification
    const body = await request.text();

    // Verify webhook signature
    const isSignatureValid = verifyWebhookSignature(signature, requestId);

    if (!isSignatureValid) {
      console.error('❌ Assinatura do webhook inválida');
      return json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse JSON from body string after verification passes
    let webhookData: WebhookData;
    try {
      webhookData = JSON.parse(body);
    } catch (parseError) {
      console.error('❌ Erro ao fazer parse do JSON do webhook:', parseError);
      return json(
        { error: 'Invalid JSON payload' },
        { status: 400 }
      );
    }

    // Process webhook with verified data
    const result = await processWebhook(webhookData);

    if (!result.success) {
      console.error('Erro ao processar webhook:', result.error);
      return json(
        { error: result.error, details: result.details },
        { status: 400 }
      );
    }

    // Webhook processado com sucesso
    console.log('Webhook processado:', result.message);
    return json({ success: true, message: 'Webhook processado' });

  } catch (error) {
    console.error('Erro no endpoint /api/webhook:', error);
    return json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
