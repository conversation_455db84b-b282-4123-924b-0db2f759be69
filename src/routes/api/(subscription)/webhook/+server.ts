import { json } from '@sveltejs/kit';
import { processWebhook } from '$lib/mp/webhook.mp.js';
import type { WebhookData } from '$lib/mp/types.mp.js';

export async function POST({ request }) {
  try {
    const webhookData: WebhookData = await request.json();
    const result = await processWebhook(webhookData);

    if (!result.success) {
      console.error('Erro ao processar webhook:', result.error);
      return json(
        { error: result.error, details: result.details },
        { status: 400 }
      );
    }

    // Webhook processado com sucesso
    console.log('Webhook processado:', result.message);
    return json({ success: true, message: 'Webhook processado' });

  } catch (error) {
    console.error('Erro no endpoint /api/webhook:', error);
    return json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
