import { match } from 'ts-pattern';
import { MP_CONFIG } from './config.mp.js';
import type { StoredPaymentData, PlanType } from './types.mp.js';

// ===== LOCALSTORAGE FUNCTIONS =====

const STORAGE_KEYS = {
  PAYMENT_ID: 'mp_payment_id',
  EXPIRES_AT: 'mp_payment_expires',
  PLAN_TYPE: 'mp_plan_type',
  AMOUNT: 'mp_amount',
  USER_EMAIL: 'mp_user_email',
  USER_NAME: 'mp_user_name',
} as const;

export const savePaymentData = (data: StoredPaymentData): void => {
  if (typeof window === 'undefined') return;
  
  localStorage.setItem(STORAGE_KEYS.PAYMENT_ID, data.paymentId);
  localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, data.expiresAt);
  localStorage.setItem(STORAGE_KEYS.PLAN_TYPE, data.planType);
  localStorage.setItem(STORAGE_KEYS.AMOUNT, data.amount.toString());
  localStorage.setItem(STORAGE_KEYS.USER_EMAIL, data.userEmail);
  localStorage.setItem(STORAGE_KEYS.USER_NAME, data.userName);
};

export const clearPaymentData = (): void => {
  if (typeof window === 'undefined') return;
  
  Object.values(STORAGE_KEYS).forEach(key => {
    localStorage.removeItem(key);
  });
};

export const getPaymentData = (): StoredPaymentData | null => {
  if (typeof window === 'undefined') return null;
  
  const paymentId = localStorage.getItem(STORAGE_KEYS.PAYMENT_ID);
  const expiresAt = localStorage.getItem(STORAGE_KEYS.EXPIRES_AT);
  const planType = localStorage.getItem(STORAGE_KEYS.PLAN_TYPE);
  const amount = localStorage.getItem(STORAGE_KEYS.AMOUNT);
  const userEmail = localStorage.getItem(STORAGE_KEYS.USER_EMAIL);
  const userName = localStorage.getItem(STORAGE_KEYS.USER_NAME);
  
  if (!paymentId || !expiresAt || !planType || !amount || !userEmail || !userName) {
    return null;
  }
  
  return {
    paymentId,
    expiresAt,
    planType: planType as StoredPaymentData['planType'],
    amount: parseFloat(amount),
    userEmail,
    userName,
  };
};

// ===== PLAN CALCULATION FUNCTIONS =====

export const PLANS = {
  monthly: { name: 'Mensal', months: 1, discount: 0, basePrice: 40.00 },
  quarterly: { name: 'Trimestral', months: 3, discount: 0.10, basePrice: 40.00 },
  semiannual: { name: 'Semestral', months: 6, discount: 0.15, basePrice: 40.00 },
  annual: { name: 'Anual', months: 12, discount: 0.20, basePrice: 40.00 },
} as const;

export const calculatePlanValue = (planType: PlanType) => {
  const plan = PLANS[planType];
  const baseValue = plan.basePrice * plan.months;
  const discount = baseValue * plan.discount;
  const totalValue = baseValue - discount;
  
  return {
    ...plan,
    totalValue: Math.round(totalValue * 100) / 100,
    monthlyValue: Math.round((totalValue / plan.months) * 100) / 100,
    savings: Math.round(discount * 100) / 100,
    savingsPercentage: Math.round(plan.discount * 100),
  };
};

export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
};

export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('pt-BR').format(dateObj);
};

// ===== VALIDATION FUNCTIONS =====

export const isPaymentExpired = (expiresAt: string): boolean => {
  const expirationDate = new Date(expiresAt);
  const now = new Date();
  return now > expirationDate;
};

export const validateUserData = (data: { name: string; email: string; cpf: string }) => {
  const errors: string[] = [];
  
  if (!data.name || data.name.trim().length < 2) {
    errors.push('Nome deve ter pelo menos 2 caracteres');
  }
  
  if (!data.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('Email inválido');
  }
  
  if (!data.cpf || !/^\d{3}\.\d{3}\.\d{3}-\d{2}$/.test(data.cpf)) {
    errors.push('CPF deve estar no formato 000.000.000-00');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const isValidPlanType = (planType: string): planType is PlanType => {
  return Object.keys(PLANS).includes(planType);
};

// ===== PIX UTILITIES =====

export const generatePixDescription = (planType: PlanType, userName: string): string => {
  const plan = PLANS[planType];
  return `${MP_CONFIG.description} - ${plan.name} - ${userName}`;
};

export const getTimeUntilExpiration = (expiresAt: string): string => {
  const expirationDate = new Date(expiresAt);
  const now = new Date();
  const diff = expirationDate.getTime() - now.getTime();
  
  if (diff <= 0) return 'Expirado';
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  
  return `${hours}h ${minutes}m`;
};

// ===== STATUS UTILITIES =====

export const translatePaymentStatus = (status: string): string => {
  return match(status)
    .with('pending', () => 'Pendente')
    .with('approved', () => 'Aprovado')
    .with('authorized', () => 'Autorizado')
    .with('in_process', () => 'Em Processamento')
    .with('in_mediation', () => 'Em Mediação')
    .with('rejected', () => 'Rejeitado')
    .with('cancelled', () => 'Cancelado')
    .with('refunded', () => 'Reembolsado')
    .with('charged_back', () => 'Contestado')
    .otherwise(() => status);
};

export const getStatusColor = (status: string): string => {
  return match(status)
    .with('pending', () => 'yellow')
    .with('approved', 'authorized', () => 'green')
    .with('in_process', () => 'blue')
    .with('in_mediation', () => 'orange')
    .with('rejected', 'cancelled', 'charged_back', () => 'red')
    .with('refunded', () => 'purple')
    .otherwise(() => 'gray');
};

// ===== WEBHOOK UTILITIES =====

export const extractPlanFromReference = (externalReference?: string) => {
  if (!externalReference) {
    return { planType: 'monthly' as const };
  }

  const parts = externalReference.split('_');
  const planType = parts[1] || 'monthly';
  
  return {
    planType: planType as 'monthly' | 'quarterly' | 'semiannual' | 'annual'
  };
};

export const extractNameFromDescription = (description: string): string => {
  const parts = description.split(' - ');
  return parts[2] || 'Usuário';
};

export const calculateEndDate = (planType: string): string => {
  const months = match(planType)
    .with('monthly', () => 1)
    .with('quarterly', () => 3)
    .with('semiannual', () => 6)
    .with('annual', () => 12)
    .otherwise(() => 1);

  const endDate = new Date();
  endDate.setMonth(endDate.getMonth() + months);
  
  return endDate.toISOString();
};
