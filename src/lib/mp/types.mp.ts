// ===== PAYMENT TYPES =====

/**
 * Dados para criar um pagamento PIX
 */
export interface CreatePaymentRequest {
  transaction_amount: number;
  description: string;
  payment_method_id: 'pix';
  payer: {
    email: string;
    first_name: string;
    last_name?: string;
    identification?: {
      type: 'CPF';
      number: string;
    };
  };
  notification_url?: string;
  external_reference?: string;
  expires?: boolean;
  expiration_date_to?: string;
}

/**
 * Status possíveis de um pagamento
 */
export type PaymentStatus = 
  | 'pending'
  | 'approved'
  | 'authorized'
  | 'in_process'
  | 'in_mediation'
  | 'rejected'
  | 'cancelled'
  | 'refunded'
  | 'charged_back';

// ===== PLAN TYPES =====

/**
 * Tipos de plano disponíveis
 */
export type PlanType = 'monthly' | 'quarterly' | 'semiannual' | 'annual';

/**
 * Configuração de um plano
 */
export interface PlanConfig {
  name: string;
  months: number;
  discount: number;
  basePrice: number;
}

/**
 * Plano calculado com valores
 */
export interface CalculatedPlan extends PlanConfig {
  totalValue: number;
  monthlyValue: number;
  savings: number;
  savingsPercentage: number;
}

/**
 * Dados do usuário para pagamento
 */
export interface UserData {
  name: string;
  email: string;
  cpf: string;
  phone?: string;
}

// ===== WEBHOOK TYPES =====

/**
 * Dados recebidos no webhook
 */
export interface WebhookData {
  id: number;
  live_mode: boolean;
  type: 'payment';
  date_created: string;
  user_id: number;
  api_version: string;
  action: 'payment.created' | 'payment.updated' | 'payment.cancelled';
  data: {
    id: string;
  };
}

/**
 * Dados do pagamento no webhook
 */
export interface WebhookPaymentData {
  id: number;
  status: PaymentStatus;
  status_detail: string;
  transaction_amount: number;
  currency_id: string;
  description: string;
  payment_method_id: string;
  date_created: string;
  date_last_updated: string;
  date_of_expiration: string;
  collector_id: number;
  payer: {
    id: number;
    email: string;
    identification: {
      type: string;
      number: string;
    };
  };
  external_reference?: string;
}

// ===== RESPONSE TYPES =====

/**
 * Resposta padrão de sucesso
 */
export interface SuccessResponse<T = any> {
  success: true;
  data: T;
  message?: string;
}

/**
 * Resposta padrão de erro
 */
export interface ErrorResponse {
  success: false;
  error: string;
  details?: any;
}

/**
 * Resposta de criação de pagamento
 */
export interface PaymentCreationResponse {
  paymentId: number;
  qrCode: string;
  qrCodeBase64: string;
  expiresAt: string;
  status: PaymentStatus;
  amount: number;
  description: string;
}

/**
 * Resposta de status de pagamento
 */
export interface PaymentStatusResponse {
  paymentId: number;
  status: PaymentStatus;
  statusDetail: string;
  amount: number;
  lastUpdated: string;
  expiresAt: string;
}

// ===== LOCALSTORAGE TYPES =====

/**
 * Dados armazenados no localStorage
 */
export interface StoredPaymentData {
  paymentId: string;
  expiresAt: string;
  planType: PlanType;
  amount: number;
  userEmail: string;
  userName: string;
}

/**
 * Dados de pagamento para localStorage (alias para StoredPaymentData)
 */
export type PaymentData = StoredPaymentData;

// ===== VALIDATION TYPES =====

/**
 * Resultado de validação
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Dados para validação de usuário
 */
export interface UserValidationData {
  name: string;
  email: string;
  cpf: string;
}

// ===== API ENDPOINT TYPES =====

/**
 * Request para criar pagamento
 */
export interface CreatePaymentApiRequest {
  planType: PlanType;
  userData: UserData;
}

/**
 * Request para consultar status
 */
export interface GetPaymentStatusRequest {
  paymentId: string;
}

// ===== FIREBASE TYPES =====

/**
 * Dados da subscription no Firebase
 */
export interface Subscription {
  id: string;
  userId: string;
  userEmail: string;
  userName: string;
  planType: PlanType;
  status: 'active' | 'expired' | 'cancelled';
  startDate: string;
  endDate: string;
  paymentId: string;
  gateway: 'mercadopago';
  amount: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Dados do usuário no Firebase
 */
export interface FirebaseUser {
  id: string;
  email: string;
  name: string;
  cpf: string;
  phone?: string;
  createdAt: string;
  updatedAt: string;
}

// ===== UTILITY TYPES =====

/**
 * Configurações do Mercado Pago
 */
export interface MPConfig {
  successUrl: string;
  failureUrl: string;
  pendingUrl: string;
  webhookUrl: string;
  currency: string;
  description: string;
  pixExpiration: number;
}

/**
 * Opções para criação de pagamento
 */
export interface PaymentOptions {
  planType: PlanType;
  userData: UserData;
  notificationUrl?: string;
  externalReference?: string;
}
