import { match } from 'ts-pattern';
import { mpPayment, MP_CONFIG, validateConfig } from './config.mp.js';
import { 
  calculatePlanValue, 
  generatePixDescription, 
  validateUserData,
  isValidPlanType 
} from './utils.mp.js';
import type {
  CreatePaymentRequest,
  PaymentStatus,
  PaymentCreationResponse,
  PaymentStatusResponse,
  UserData,
  PlanType,
  SuccessResponse,
  ErrorResponse
} from './types.mp.js';

export const createPayment = async (
  planType: PlanType,
  userData: UserData
): Promise<SuccessResponse<PaymentCreationResponse> | ErrorResponse> => {
  try {
    if (!validateConfig()) {
      return { success: false, error: 'Configuração inválida' };
    }

    const userValidation = validateUserData(userData);
    if (!userValidation.isValid) {
      return {
        success: false,
        error: 'Dados do usuário inválidos',
        details: userValidation.errors
      };
    }

    if (!isValidPlanType(planType)) {
      return { success: false, error: 'Tipo de plano inválido' };
    }

    const plan = calculatePlanValue(planType);
    const [firstName, ...lastNameParts] = userData.name.trim().split(' ');
    const lastName = lastNameParts.join(' ') || '';

    const paymentRequest: CreatePaymentRequest = {
      transaction_amount: plan.totalValue,
      description: generatePixDescription(planType, userData.name),
      payment_method_id: 'pix',
      payer: {
        email: userData.email,
        first_name: firstName,
        last_name: lastName || undefined,
        identification: {
          type: 'CPF',
          number: userData.cpf.replace(/\D/g, '')
        }
      },
      notification_url: MP_CONFIG.webhookUrl,
      external_reference: `plan_${planType}_${Date.now()}`,
      expires: true,
      expiration_date_to: new Date(Date.now() + MP_CONFIG.pixExpiration * 1000).toISOString()
    };

    const payment = await mpPayment.create({ body: paymentRequest });

    const response: PaymentCreationResponse = {
      paymentId: payment.id!,
      qrCode: payment.point_of_interaction?.transaction_data?.qr_code || '',
      qrCodeBase64: payment.point_of_interaction?.transaction_data?.qr_code_base64 || '',
      expiresAt: payment.date_of_expiration || '',
      status: (payment.status as PaymentStatus) || 'pending',
      amount: payment.transaction_amount || 0,
      description: payment.description || ''
    };

    return {
      success: true,
      data: response,
      message: 'Pagamento PIX criado'
    };

  } catch (error) {
    console.error('Erro ao criar pagamento:', error);
    return {
      success: false,
      error: 'Erro ao criar pagamento PIX',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
};

export const getPaymentStatus = async (
  paymentId: string
): Promise<SuccessResponse<PaymentStatusResponse> | ErrorResponse> => {
  try {
    if (!validateConfig()) {
      return { success: false, error: 'Configuração inválida' };
    }

    if (!paymentId || isNaN(Number(paymentId))) {
      return { success: false, error: 'ID do pagamento inválido' };
    }

    const payment = await mpPayment.get({ id: Number(paymentId) });

    const response: PaymentStatusResponse = {
      paymentId: payment.id!,
      status: (payment.status as PaymentStatus) || 'pending',
      statusDetail: payment.status_detail || '',
      amount: payment.transaction_amount || 0,
      lastUpdated: payment.date_last_updated || '',
      expiresAt: payment.date_of_expiration || ''
    };

    return {
      success: true,
      data: response,
      message: 'Status consultado'
    };

  } catch (error) {
    console.error('Erro ao consultar status:', error);
    return {
      success: false,
      error: 'Erro ao consultar status',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
};

export const cancelPayment = async (
  paymentId: string
): Promise<SuccessResponse | ErrorResponse> => {
  try {
    if (!validateConfig()) {
      return { success: false, error: 'Configuração inválida' };
    }

    if (!paymentId || isNaN(Number(paymentId))) {
      return { success: false, error: 'ID do pagamento inválido' };
    }

    await mpPayment.cancel({ id: Number(paymentId) });

    return {
      success: true,
      data: null,
      message: 'Pagamento cancelado'
    };

  } catch (error) {
    console.error('Erro ao cancelar pagamento:', error);
    return {
      success: false,
      error: 'Erro ao cancelar pagamento',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
};

export const isPaymentApproved = (status: PaymentStatus): boolean => {
  return match(status)
    .with('approved', 'authorized', () => true)
    .otherwise(() => false);
};

export const isPaymentRejected = (status: PaymentStatus): boolean => {
  return match(status)
    .with('rejected', 'cancelled', () => true)
    .otherwise(() => false);
};

export const isPaymentPending = (status: PaymentStatus): boolean => {
  return match(status)
    .with('pending', 'in_process', () => true)
    .otherwise(() => false);
};

export const mercadoPagoService = {
  createPayment,
  getPaymentStatus,
  cancelPayment,
  isPaymentApproved,
  isPaymentRejected,
  isPaymentPending
};

export default mercadoPagoService;
