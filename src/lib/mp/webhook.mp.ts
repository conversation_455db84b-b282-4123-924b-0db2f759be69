import { match } from 'ts-pattern';
import { mpPayment, validateConfig } from './config.mp.js';
import { 
  extractPlanFromReference, 
  extractNameFromDescription, 
  calculateEndDate 
} from './utils.mp.js';
import type {
  WebhookData,
  WebhookPaymentData,
  PaymentStatus,
  SuccessResponse,
  ErrorResponse,
  Subscription
} from './types.mp.js';

export const processWebhook = async (
  webhookData: WebhookData
): Promise<SuccessResponse | ErrorResponse> => {
  try {
    if (!validateConfig()) {
      return { success: false, error: 'Configuração inválida' };
    }

    if (webhookData.type !== 'payment' || !webhookData.data?.id) {
      return { success: false, error: 'Dados do webhook inválidos' };
    }

    const paymentResult = await getPaymentDetails(webhookData.data.id);
    if (!paymentResult.success) return paymentResult;

    return match(paymentResult.data.status)
      .with('approved', () => handleApprovedPayment(paymentResult.data))
      .with('rejected', 'cancelled', () => handleRejectedPayment(paymentResult.data))
      .with('pending', 'in_process', () => handlePendingPayment(paymentResult.data))
      .otherwise(() => ({
        success: true,
        data: null,
        message: `Status não processado: ${paymentResult.data.status}`
      }));

  } catch (error) {
    console.error('Erro ao processar webhook:', error);
    return {
      success: false,
      error: 'Erro ao processar webhook',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
};

export const getPaymentDetails = async (
  paymentId: string
): Promise<SuccessResponse<WebhookPaymentData> | ErrorResponse> => {
  try {
    if (!paymentId || isNaN(Number(paymentId))) {
      return { success: false, error: 'ID do pagamento inválido' };
    }

    const payment = await mpPayment.get({ id: Number(paymentId) });

    const paymentData: WebhookPaymentData = {
      id: payment.id!,
      status: (payment.status as PaymentStatus) || 'pending',
      status_detail: payment.status_detail || '',
      transaction_amount: payment.transaction_amount || 0,
      currency_id: payment.currency_id || 'BRL',
      description: payment.description || '',
      payment_method_id: payment.payment_method_id || '',
      date_created: payment.date_created || '',
      date_last_updated: payment.date_last_updated || '',
      date_of_expiration: payment.date_of_expiration || '',
      collector_id: payment.collector_id || 0,
      payer: {
        id: Number(payment.payer?.id) || 0,
        email: payment.payer?.email || '',
        identification: {
          type: payment.payer?.identification?.type || '',
          number: payment.payer?.identification?.number || ''
        }
      },
      external_reference: payment.external_reference
    };

    return {
      success: true,
      data: paymentData,
      message: 'Dados do pagamento obtidos'
    };

  } catch (error) {
    console.error('Erro ao buscar dados do pagamento:', error);
    return {
      success: false,
      error: 'Erro ao buscar dados do pagamento',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
};

export const handleApprovedPayment = async (
  payment: WebhookPaymentData
): Promise<SuccessResponse | ErrorResponse> => {
  try {
    console.log(`✅ Pagamento aprovado: ${payment.id}`);
    const planInfo = extractPlanFromReference(payment.external_reference);
    const subscription: Subscription = {
      id: `sub_${payment.id}_${Date.now()}`,
      userId: `user_${payment.payer.id}`,
      userEmail: payment.payer.email,
      userName: extractNameFromDescription(payment.description),
      planType: planInfo.planType,
      status: 'active',
      startDate: new Date().toISOString(),
      endDate: calculateEndDate(planInfo.planType),
      paymentId: payment.id.toString(),
      gateway: 'mercadopago',
      amount: payment.transaction_amount,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // TODO: Salvar subscription no Firebase
    // await saveSubscriptionToFirebase(subscription);

    return {
      success: true,
      data: subscription,
      message: 'Subscription criada'
    };

  } catch (error) {
    console.error('Erro ao processar pagamento aprovado:', error);
    return {
      success: false,
      error: 'Erro ao processar pagamento aprovado',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
};

export const handleRejectedPayment = async (
  payment: WebhookPaymentData
): Promise<SuccessResponse | ErrorResponse> => {
  try {
    console.log(`❌ Pagamento rejeitado: ${payment.id}`);

    // TODO: Atualizar subscription para 'cancelled' se existir
    // await updateSubscriptionStatus(payment.id.toString(), 'cancelled');

    return {
      success: true,
      data: null,
      message: 'Pagamento rejeitado processado'
    };

  } catch (error) {
    console.error('Erro ao processar pagamento rejeitado:', error);
    return {
      success: false,
      error: 'Erro ao processar pagamento rejeitado',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
};

export const handlePendingPayment = async (
  payment: WebhookPaymentData
): Promise<SuccessResponse | ErrorResponse> => {
  try {
    console.log(`⏳ Pagamento pendente: ${payment.id}`);

    return {
      success: true,
      data: null,
      message: 'Pagamento pendente processado'
    };

  } catch (error) {
    console.error('Erro ao processar pagamento pendente:', error);
    return {
      success: false,
      error: 'Erro ao processar pagamento pendente',
      details: error instanceof Error ? error.message : 'Erro desconhecido'
    };
  }
};

export const webhookService = {
  processWebhook,
  getPaymentDetails,
  handleApprovedPayment,
  handleRejectedPayment,
  handlePendingPayment
};

export default webhookService;
