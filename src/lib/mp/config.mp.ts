import { MercadoPagoConfig, Payment } from 'mercadopago';
import { env } from '$env/dynamic/private';

const mpConfig = new MercadoPagoConfig({
  accessToken: env.MERCADO_PAGO_ACCESS_TOKEN || '',
});

export const mpPayment = new Payment(mpConfig);

export const MP_CONFIG = {
  successUrl: env.SUCCESS_URL || 'http://localhost:5173/checkout/success',
  failureUrl: env.FAILURE_URL || 'http://localhost:5173/checkout/failed',
  pendingUrl: env.PENDING_URL || 'http://localhost:5173/checkout/pending',

  webhookUrl: env.WEBHOOK_URL || 'http://localhost:5173/api/webhook',
  webhookSecret: env.MERCADO_PAGO_WEBHOOK_SECRET || '',

  currency: 'BRL',
  description: 'Plano de Treinamento',

  pixExpiration: 24 * 60 * 60,
} as const;

export const validateConfig = (): boolean => {
  if (!env.MERCADO_PAGO_ACCESS_TOKEN) {
    console.error('❌ MERCADO_PAGO_ACCESS_TOKEN não configurado');
    return false;
  }

  if (!env.MERCADO_PAGO_WEBHOOK_SECRET) {
    console.error('❌ MERCADO_PAGO_WEBHOOK_SECRET não configurado');
    return false;
  }

  console.log('✅ Configuração do Mercado Pago carregada');
  return true;
};

export default {
  mpPayment,
  config: MP_CONFIG,
  validate: validateConfig,
};
